#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules/postject/dist/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules/postject/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules/postject/dist/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules/postject/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/postject@1.0.0-alpha.6/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../postject@1.0.0-alpha.6/node_modules/postject/dist/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../postject@1.0.0-alpha.6/node_modules/postject/dist/cli.js" "$@"
fi
