{"version": 3, "file": "clang-fetcher.js", "sourceRoot": "", "sources": ["../src/clang-fetcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAoC;AACpC,kDAA0B;AAC1B,6CAA+B;AAC/B,2CAA6B;AAC7B,yCAA2B;AAC3B,2CAA6B;AAC7B,2CAA+C;AAC/C,uCAAkC;AAClC,uDAAyD;AAEzD,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAM,OAAO,GAAG,iEAAiE,CAAC;AAElF,SAAS,oBAAoB,CAAC,MAAc,EAAE,QAAgB;IAC5D,MAAM,SAAS,GAAG;QACd,OAAO,EAAE,WAAW;QACpB,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,KAAK;KACjB,CAAC;IACF,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/B,IAAI,MAAM,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,EAAE;QAC5C,MAAM,GAAG,WAAW,CAAC;KACtB;IACD,OAAO,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;AACtC,CAAC;AAED,SAAS,mBAAmB,CAAC,WAAmB,EAAE,cAAsB,EAAE,MAAc,EAAE,QAAgB;IACxG,MAAM,OAAO,GAAG,GAAG,WAAW,IAAI,cAAc,MAAM,CAAC;IACvD,OAAO,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,OAAO,CAAC;AAC1D,CAAC;AAED,SAAS,UAAU;IACjB,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO;QAAE,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IACpD,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC;IAChF,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;AAClC,CAAC;AAEM,KAAK,UAAU,uBAAuB,CAAC,eAAuB,EAAE,UAAkB;IACvF,MAAM,gBAAgB,GAAG,MAAM,oBAAoB,CAAC,eAAe,CAAC,CAAC;IAErE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACvD,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACjC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC;KAC3C;IAED,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,iBAAiB,QAAQ,EAAE,CAAC,CAAC;KACxE;IAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChC,MAAM,WAAW,GAAG,MAAM,IAAA,sCAAoB,EAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QAC5E,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;KAC1C;IAED,OAAO;QACL,GAAG,EAAE;YACH,EAAE,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACjE,GAAG,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;SACrE;QACD,IAAI,EAAE,OAAO;KACd,CAAC;AACJ,CAAC;AA3BD,0DA2BC;AAED,SAAS,wBAAwB,CAAC,MAAc;IAC9C,MAAM,KAAK,GAAG,2DAA2D,CAAC;IAC1E,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,iBAAiB;QAAE,OAAO,IAAI,CAAC;IACpC,MAAM,CAAC,EAAC,YAAY,EAAE,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;IAC5D,OAAO,GAAG,YAAY,IAAI,gBAAgB,EAAE,CAAC;AAC/C,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAc;IACzC,MAAM,KAAK,GAAG,2FAA2F,CAAC;IAC1G,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,iBAAiB;QAAE,OAAO,IAAI,CAAC;IACpC,MAAM,CAAC,EAAC,YAAY,EAAE,QAAQ,EAAE,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;IACtE,OAAO,GAAG,QAAQ,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AACxE,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,eAAuB;IACzD,CAAC,CAAC,8BAA8B,EAAE,eAAe,CAAC,CAAC;IACnD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,4BAAgB,EAAE,GAAG,eAAe,QAAQ,CAAC,CAAC;IAChF,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAAE,OAAO,YAAY,CAAC;IACzF,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,4BAAgB,CAAC;QAAE,MAAM,EAAE,CAAC,MAAM,CAAC,4BAAgB,CAAC,CAAC;IAE9E,MAAM,YAAY,GAAG,MAAM,IAAA,eAAK,EAAC,wDAAwD,eAAe,OAAO,EAAE,MAAM,CAAC,CAAC;IACzH,MAAM,yBAAyB,GAAG,mCAAmC,CAAC;IACtE,MAAM,qBAAqB,GAAG,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC3E,IAAI,CAAC,qBAAqB;QAAE,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;IAChH,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,CAAC;IAEpD,MAAM,iBAAiB,GAAG,MAAM,IAAA,eAAK,EAAC,wDAAwD,gBAAgB,4CAA4C,EAAE,MAAM,CAAC,CAAC;IACpK,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAE9E,MAAM,kBAAkB,GAAG,wBAAwB,CAAC,WAAW,CAAC,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACrG,IAAI,CAAC,kBAAkB;QAAE,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IACrG,CAAC,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;IAEzC,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE1G,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAK,EAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACzD,CAAC,CAAC,iBAAiB,CAAC,CAAC;IACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,4BAAgB,EAAE,GAAG,eAAe,YAAY,CAAC,CAAC;IAC/E,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;QAAE,MAAM,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC3D,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC9B,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAC1B,MAAM,GAAG,CAAC,CAAC,CAAC;QACV,IAAI,EAAE,OAAO;QACb,GAAG,EAAE,YAAY;KAClB,CAAC,CAAC;IACH,MAAM,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzB,CAAC,CAAC,4BAA4B,CAAC,CAAC;IAChC,OAAO,YAAY,CAAC;AACtB,CAAC"}