<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="./dist/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>برنامج استحقاق كشف عمل - علي عاجل خشان المحنّة</title>
    <script type="module" crossorigin src="./dist/assets/index-BWWQOl9G.js"></script>
    <link rel="stylesheet" crossorigin href="./dist/assets/index-QQmYXolK.css">
    <style>
      /* إضافة بعض الأنماط الاحتياطية في حالة عدم تحميل CSS */
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        direction: rtl;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* رسالة تحميل */
      .loading-message {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px 40px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        text-align: center;
        font-size: 18px;
        color: #333;
        z-index: 9999;
      }
      
      .loading-spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 10px auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- رسالة التحميل خارج عنصر root -->
    <div class="loading-message" id="loading-overlay">
      <div style="font-size: 2rem; margin-bottom: 10px;">🌟</div>
      <h2 style="margin: 10px 0; color: #667eea;">برنامج استحقاق كشف عمل</h2>
      <div class="loading-spinner"></div>
      <p>جاري تحميل البرنامج...</p>
      <p style="font-size: 16px; color: #667eea; font-weight: bold; margin-top: 15px;">
        👨‍💻 المبرمج: علي عاجل خشان المحنّة
      </p>
      <p style="font-size: 12px; color: #999; margin-top: 10px;">الإصدار 2.0 - نسخة React متقدمة</p>
    </div>

    <!-- عنصر root فارغ للتطبيق -->
    <div id="root"></div>
    
    <script>
      // إخفاء رسالة التحميل بعد تحميل التطبيق
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingOverlay = document.getElementById('loading-overlay');
          if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
          }
        }, 2000);
      });
      
      // التحقق من تحميل React والتطبيق
      let checkCount = 0;
      const maxChecks = 10;

      function checkAppStatus() {
        checkCount++;
        const loadingOverlay = document.getElementById('loading-overlay');
        const rootElement = document.getElementById('root');

        // التحقق من وجود محتوى في root
        if (rootElement && rootElement.children.length > 0) {
          // التطبيق تم تحميله بنجاح
          if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
          }
          return;
        }

        // إذا وصلنا للحد الأقصى من المحاولات
        if (checkCount >= maxChecks) {
          if (loadingOverlay) {
            // جمع معلومات التشخيص
            const diagnostics = [];
            diagnostics.push(`React موجود: ${!!window.React}`);
            diagnostics.push(`ReactDOM موجود: ${!!window.ReactDOM}`);
            diagnostics.push(`عنصر root موجود: ${!!rootElement}`);
            diagnostics.push(`محتوى root: ${rootElement ? rootElement.children.length : 'غير موجود'}`);

            // فحص الأخطاء في console
            let consoleErrors = 'لا توجد أخطاء مسجلة';
            if (window.lastError) {
              consoleErrors = window.lastError;
            }

            loadingOverlay.innerHTML = `
              <div style="font-size: 2rem; margin-bottom: 10px;">⚠️</div>
              <h3 style="color: #e74c3c; margin-bottom: 15px;">خطأ في تحميل البرنامج</h3>
              <p style="margin-bottom: 10px;">لم يتم تحميل التطبيق بشكل صحيح.</p>

              <div style="background: #f8f9fa; color: #333; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: right; font-size: 12px;">
                <strong>معلومات التشخيص:</strong><br>
                ${diagnostics.join('<br>')}
                <br><br>
                <strong>آخر خطأ:</strong><br>
                ${consoleErrors}
              </div>

              <p style="font-size: 14px; color: #666; margin-bottom: 15px;">
                تأكد من وجود مجلد "dist" مع ملفاته في نفس مكان هذا الملف.
              </p>
              <p style="font-size: 14px; color: #667eea; font-weight: bold; margin-bottom: 15px;">
                👨‍💻 المبرمج: علي عاجل خشان المحنّة
              </p>

              <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <button onclick="location.reload()" style="
                  background: #667eea; color: white; border: none; padding: 12px 24px;
                  border-radius: 8px; cursor: pointer; font-size: 14px;
                ">🔄 إعادة المحاولة</button>

                <button onclick="window.open('برنامج-مستقل.html', '_blank')" style="
                  background: #28a745; color: white; border: none; padding: 12px 24px;
                  border-radius: 8px; cursor: pointer; font-size: 14px;
                ">🚀 النسخة المستقلة</button>

                <button onclick="window.open('اختبار-تحميل.html', '_blank')" style="
                  background: #ffc107; color: #333; border: none; padding: 12px 24px;
                  border-radius: 8px; cursor: pointer; font-size: 14px;
                ">🔍 اختبار التحميل</button>
              </div>
            `;
          }
          return;
        }

        // المحاولة التالية بعد 500ms
        setTimeout(checkAppStatus, 500);
      }

      // بدء فحص حالة التطبيق بعد ثانية واحدة
      setTimeout(checkAppStatus, 1000);

      // تسجيل الأخطاء
      window.addEventListener('error', function(e) {
        window.lastError = e.message + ' في ' + e.filename + ':' + e.lineno;
        console.error('خطأ JavaScript:', e);
      });
    </script>
  </body>
</html>
