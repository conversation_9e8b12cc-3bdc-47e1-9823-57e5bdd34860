<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="./dist/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>برنامج استحقاق كشف عمل - علي عاجل خشان المحنّة</title>
    <script type="module" crossorigin src="./dist/assets/index-BWWQOl9G.js"></script>
    <link rel="stylesheet" crossorigin href="./dist/assets/index-QQmYXolK.css">
    <style>
      /* إضافة بعض الأنماط الاحتياطية في حالة عدم تحميل CSS */
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        direction: rtl;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* رسالة تحميل */
      .loading-message {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px 40px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        text-align: center;
        font-size: 18px;
        color: #333;
        z-index: 9999;
      }
      
      .loading-spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 10px auto;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading-message">
        <div class="loading-spinner"></div>
        <p>جاري تحميل برنامج استحقاق كشف عمل...</p>
        <p style="font-size: 14px; color: #666;">المبرمج: علي عاجل خشان المحنّة</p>
      </div>
    </div>
    
    <script>
      // إخفاء رسالة التحميل بعد تحميل التطبيق
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingMessage = document.querySelector('.loading-message');
          if (loadingMessage) {
            loadingMessage.style.display = 'none';
          }
        }, 2000);
      });
      
      // التحقق من تحميل React
      setTimeout(function() {
        if (!window.React) {
          const loadingMessage = document.querySelector('.loading-message');
          if (loadingMessage) {
            loadingMessage.innerHTML = `
              <h3 style="color: #e74c3c;">⚠️ خطأ في التحميل</h3>
              <p>لم يتم تحميل ملفات البرنامج بشكل صحيح.</p>
              <p style="font-size: 14px;">تأكد من وجود مجلد "dist" في نفس مكان هذا الملف.</p>
              <button onclick="location.reload()" style="
                background: #667eea;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 10px;
              ">إعادة المحاولة</button>
            `;
          }
        }
      }, 5000);
    </script>
  </body>
</html>
