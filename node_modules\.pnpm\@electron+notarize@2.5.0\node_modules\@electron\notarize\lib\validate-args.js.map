{"version": 3, "file": "validate-args.js", "sourceRoot": "", "sources": ["../src/validate-args.ts"], "names": [], "mappings": ";;;AAUA,kBAAkB;AAClB,SAAgB,2BAA2B,CACzC,IAA+B;IAE/B,MAAM,KAAK,GAAG,IAAyC,CAAC;IACxD,OAAO,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,KAAK,CAAC,eAAe,KAAK,SAAS,CAAC;AAC5E,CAAC;AALD,kEAKC;AAED,kBAAkB;AAClB,SAAgB,yBAAyB,CACvC,IAA+B;IAE/B,MAAM,KAAK,GAAG,IAAuC,CAAC;IACtD,OAAO,KAAK,CAAC,WAAW,KAAK,SAAS,IAAI,KAAK,CAAC,cAAc,KAAK,SAAS,CAAC;AAC/E,CAAC;AALD,8DAKC;AAED,kBAAkB;AAClB,SAAgB,+BAA+B,CAC7C,IAA+B;IAE/B,MAAM,UAAU,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;IACrD,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACjD,IAAI,UAAU,IAAI,QAAQ,EAAE;QAC1B,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;KACzF;IACD,IAAI,UAAU,EAAE;QACd,MAAM,aAAa,GAAG,IAAyC,CAAC;QAChE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,+EAA+E,CAChF,CAAC;SACH;aAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE;YACzC,MAAM,IAAI,KAAK,CACb,+EAA+E,CAChF,CAAC;SACH;QACD,OAAO,aAAa,CAAC;KACtB;IACD,IAAI,QAAQ,EAAE;QACZ,MAAM,WAAW,GAAG,IAAuC,CAAC;QAC5D,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;SACH;aAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACtC,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;SACH;QACD,OAAO,WAAW,CAAC;KACpB;IACD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;AACvF,CAAC;AAnCD,0EAmCC;AAED,SAAgB,+BAA+B,CAC7C,IAA2B;IAE3B,MAAM,KAAK,GAAG,IAAqC,CAAC;IACpD,OAAO,CACL,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,KAAK,CAAC,eAAe,KAAK,SAAS,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CACjG,CAAC;AACJ,CAAC;AAPD,0EAOC;AAED,SAAgB,6BAA6B,CAC3C,IAA2B;IAE3B,MAAM,KAAK,GAAG,IAAmC,CAAC;IAClD,OAAO,CACL,KAAK,CAAC,cAAc,KAAK,SAAS;QAClC,KAAK,CAAC,WAAW,KAAK,SAAS;QAC/B,KAAK,CAAC,aAAa,KAAK,SAAS,CAClC,CAAC;AACJ,CAAC;AATD,sEASC;AAED,SAAgB,+BAA+B,CAC7C,IAA2B;IAE3B,MAAM,KAAK,GAAG,IAAqC,CAAC;IACpD,OAAO,KAAK,CAAC,QAAQ,KAAK,SAAS,IAAI,KAAK,CAAC,eAAe,KAAK,SAAS,CAAC;AAC7E,CAAC;AALD,0EAKC;AAED;;GAEG;AACH,SAAgB,mCAAmC,CACjD,IAA2B;IAE3B,MAAM,UAAU,GAAG,+BAA+B,CAAC,IAAI,CAAC,CAAC;IACzD,MAAM,QAAQ,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;IACrD,MAAM,UAAU,GAAG,+BAA+B,CAAC,IAAI,CAAC,CAAC;IACzD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACxE,MAAM,IAAI,KAAK,CACb,uFAAuF,CACxF,CAAC;KACH;IACD,IAAI,UAAU,EAAE;QACd,MAAM,aAAa,GAAG,IAAqC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,oFAAoF,CACrF,CAAC;SACH;aAAM,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE;YACzC,MAAM,IAAI,KAAK,CACb,4FAA4F,CAC7F,CAAC;SACH;aAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YAChC,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAC;SACH;QACD,OAAO,aAAa,CAAC;KACtB;IACD,IAAI,QAAQ,EAAE;QACZ,MAAM,WAAW,GAAG,IAAmC,CAAC;QACxD,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAC;SACH;aAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;YACtC,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;SACH;aAAM,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;YACrC,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;SACH;QACD,OAAO,WAAW,CAAC;KACpB;IACD,IAAI,UAAU,EAAE;QACd,MAAM,aAAa,GAAG,IAAqC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE;YAClC,MAAM,IAAI,KAAK,CACb,4FAA4F,CAC7F,CAAC;SACH;QACD,OAAO,aAAa,CAAC;KACtB;IACD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;AACjG,CAAC;AAvDD,kFAuDC"}