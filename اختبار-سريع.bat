@echo off
chcp 65001 >nul
title اختبار سريع - برنامج استحقاق كشف عمل

echo.
echo ========================================
echo    اختبار سريع للبرنامج
echo    المبرمج: علي عاجل خشان المحنّة
echo ========================================
echo.

echo 🔍 فحص الملفات المطلوبة...
echo.

:: فحص مجلد dist
if not exist "dist" (
    echo ❌ مجلد "dist" غير موجود!
    echo 🔨 جاري بناء المشروع...
    pnpm run build
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء المشروع!
        pause
        exit /b 1
    )
    echo ✅ تم بناء المشروع بنجاح!
) else (
    echo ✅ مجلد "dist" موجود
)

:: فحص ملفات JavaScript
if exist "dist\assets\*.js" (
    echo ✅ ملفات JavaScript موجودة
) else (
    echo ❌ ملفات JavaScript غير موجودة!
    echo 🔨 جاري بناء المشروع...
    pnpm run build
)

:: فحص ملفات CSS
if exist "dist\assets\*.css" (
    echo ✅ ملفات CSS موجودة
) else (
    echo ❌ ملفات CSS غير موجودة!
    echo 🔨 جاري بناء المشروع...
    pnpm run build
)

echo.
echo 🚀 تشغيل البرنامج...
echo.

:: تشغيل ملف الاختبار أولاً
echo 📋 فتح ملف الاختبار...
start "" "اختبار-تحميل.html"

:: انتظار قليل ثم تشغيل البرنامج الرئيسي
timeout /t 2 /nobreak >nul
echo 🌟 فتح البرنامج الرئيسي...
start "" "برنامج-استحقاق-كشف-عمل.html"

:: انتظار قليل ثم تشغيل النسخة المستقلة للمقارنة
timeout /t 2 /nobreak >nul
echo 📱 فتح النسخة المستقلة للمقارنة...
start "" "برنامج-مستقل.html"

echo.
echo ========================================
echo تم فتح ثلاث نوافذ:
echo 1. ملف الاختبار - لفحص التحميل
echo 2. البرنامج الرئيسي - النسخة المتقدمة
echo 3. النسخة المستقلة - للمقارنة
echo ========================================
echo.
echo قارن بين النوافذ لتحديد المشكلة
echo.
pause
