#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/bin/node-gyp.js" "$@"
else
  exec node  "$basedir/../../../../../../@electron+node-gyp@https+++_00edca533b603d6dc5ad01ce0c26cce3/node_modules/@electron/node-gyp/bin/node-gyp.js" "$@"
fi
