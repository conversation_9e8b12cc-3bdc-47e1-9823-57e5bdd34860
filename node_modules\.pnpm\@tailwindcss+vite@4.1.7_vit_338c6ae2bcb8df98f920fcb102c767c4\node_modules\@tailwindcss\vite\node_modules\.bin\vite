#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../vite@6.3.5_@types+node@24.0_5b7164c74c7d83357ddd9b81089c1428/node_modules/vite/bin/vite.js" "$@"
fi
