{"name": "@malept/cross-spawn-promise", "version": "2.0.0", "description": "Promisified version of cross-spawn", "main": "dist/src/index.js", "repository": "https://github.com/malept/cross-spawn-promise", "author": "<PERSON>", "license": "Apache-2.0", "engines": {"node": ">= 12.13.0"}, "scripts": {"ava": "ava", "build": "tsc", "coverage": "nyc --reporter=lcov --reporter=text ava", "docs": "typedoc src/index.ts", "lint": "prettier --check . && eslint --ext .ts .", "prepare": "husky install && yarn build", "test": "yarn lint && yarn ava"}, "files": ["dist"], "dependencies": {"cross-spawn": "^7.0.1"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/cross-spawn": "^6.0.1", "@types/node": "^15.14.0", "@typescript-eslint/eslint-plugin": "^4.0.1", "@typescript-eslint/parser": "^4.0.1", "ava": "^3.3.0", "eslint": "^7.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-ava": "^12.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-tsdoc": "^0.2.5", "husky": "^7.0.0", "lint-staged": "^11.0.0", "nyc": "^15.0.0", "prettier": "^2.0.1", "source-map-support": "^0.5.16", "ts-node": "^10.0.0", "typedoc": "^0.21.2", "typescript": "~4.3.5"}, "ava": {"extensions": ["ts"], "require": ["ts-node/register"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "2019", "sourceType": "module"}, "plugins": ["@typescript-eslint", "eslint-plugin-tsdoc"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "plugin:prettier/recommended", "plugin:promise/recommended", "prettier"], "rules": {"strict": "error", "tsdoc/syntax": "warn"}}, "eslintIgnore": ["dist"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.malept-cross-spawn-promise?utm_medium=referral&utm_source=npm_fund"}], "lint-staged": {"*.{json,md,yml}": "prettier --write", "*.ts": ["prettier --write", "eslint --fix"]}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript"}}