#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/read-binary-file-arch@1.0.6/node_modules/read-binary-file-arch/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/read-binary-file-arch@1.0.6/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/read-binary-file-arch@1.0.6/node_modules/read-binary-file-arch/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/read-binary-file-arch@1.0.6/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../read-binary-file-arch@1.0.6/node_modules/read-binary-file-arch/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../read-binary-file-arch@1.0.6/node_modules/read-binary-file-arch/cli.js" "$@"
fi
