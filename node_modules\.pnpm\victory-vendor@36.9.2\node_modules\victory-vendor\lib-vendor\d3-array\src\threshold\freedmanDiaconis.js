"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = thresholdFreedmanDiaconis;

var _count = _interopRequireDefault(require("../count.js"));

var _quantile = _interopRequireDefault(require("../quantile.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function thresholdFreedmanDiaconis(values, min, max) {
  return Math.ceil((max - min) / (2 * ((0, _quantile.default)(values, 0.75) - (0, _quantile.default)(values, 0.25)) * Math.pow((0, _count.default)(values), -1 / 3)));
}