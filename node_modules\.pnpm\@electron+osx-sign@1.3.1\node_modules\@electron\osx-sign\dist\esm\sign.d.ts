import { SignOptions } from './types';
/**
 * Signs a macOS application.
 * @returns A void Promise once the signing operation is complete.
 *
 * @category Codesign
 */
export declare function signApp(_opts: SignOptions): Promise<void>;
/**
 * This function is a legacy callback implementation.
 *
 * @deprecated Please use the Promise-based {@link signAsync} method instead.
 * @category Codesign
 */
export declare const sign: (opts: SignOptions, cb?: ((error?: Error) => void) | undefined) => void;
