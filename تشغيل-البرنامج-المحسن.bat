@echo off
chcp 65001 >nul
title برنامج استحقاق كشف عمل - علي عاجل خشان المحنّة

echo.
echo ========================================
echo    برنامج استحقاق كشف عمل
echo    المبرمج: علي عاجل خشان المحنّة
echo ========================================
echo.

:: التحقق من وجود مجلد dist
if not exist "dist" (
    echo ❌ خطأ: مجلد "dist" غير موجود!
    echo.
    echo يجب بناء المشروع أولاً باستخدام:
    echo pnpm run build
    echo.
    pause
    exit /b 1
)

:: التحقق من وجود ملفات JavaScript و CSS
if not exist "dist\assets\*.js" (
    echo ❌ خطأ: ملفات JavaScript غير موجودة في مجلد dist\assets!
    echo.
    echo يجب بناء المشروع أولاً باستخدام:
    echo pnpm run build
    echo.
    pause
    exit /b 1
)

if not exist "dist\assets\*.css" (
    echo ❌ خطأ: ملفات CSS غير موجودة في مجلد dist\assets!
    echo.
    echo يجب بناء المشروع أولاً باستخدام:
    echo pnpm run build
    echo.
    pause
    exit /b 1
)

echo ✅ جميع الملفات المطلوبة موجودة
echo.

:: عرض خيارات التشغيل
echo اختر طريقة التشغيل:
echo.
echo 1. تشغيل النسخة المستقلة (HTML بسيط)
echo 2. تشغيل النسخة المتقدمة (React)
echo 3. تشغيل كخادم محلي
echo 4. بناء المشروع أولاً ثم التشغيل
echo 5. خروج
echo.

set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🚀 تشغيل النسخة المستقلة...
    start "" "برنامج-مستقل.html"
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 🚀 تشغيل النسخة المتقدمة...
    start "" "برنامج-استحقاق-كشف-عمل.html"
    goto end
)

if "%choice%"=="3" (
    echo.
    echo 🌐 تشغيل الخادم المحلي...
    echo سيتم فتح البرنامج في المتصفح على العنوان: http://localhost:3000
    echo.
    node خادم-محلي.js
    goto end
)

if "%choice%"=="4" (
    echo.
    echo 🔨 بناء المشروع...
    pnpm run build
    if %errorlevel% equ 0 (
        echo.
        echo ✅ تم بناء المشروع بنجاح!
        echo 🚀 تشغيل النسخة المتقدمة...
        start "" "برنامج-استحقاق-كشف-عمل.html"
    ) else (
        echo.
        echo ❌ فشل في بناء المشروع!
        echo تأكد من تثبيت الحزم باستخدام: pnpm install
    )
    goto end
)

if "%choice%"=="5" (
    echo.
    echo 👋 شكراً لاستخدام البرنامج!
    goto end
)

echo.
echo ❌ اختيار غير صحيح!
pause
goto start

:end
echo.
echo ========================================
echo شكراً لاستخدام برنامج استحقاق كشف عمل
echo المبرمج: علي عاجل خشان المحنّة
echo ========================================
pause
