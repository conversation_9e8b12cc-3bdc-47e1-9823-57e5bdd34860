{"version": 3, "file": "node-pre-gyp.js", "sourceRoot": "", "sources": ["../../src/module-type/node-pre-gyp.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,qEAAoD;AACpD,iEAA2D;AAE3D,wBAA+C;AAC/C,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAa,UAAW,SAAQ,eAAY;IAC1C,KAAK,CAAC,QAAQ;QACZ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QACzE,OAAO,CAAC,CAAC,WAAW,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAC9B,OAAO,IAAA,eAAY,EACjB,IAAI,CAAC,UAAU,EACf,gBAAgB,WAAW,mBAAmB,CAC/C,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,cAAsB;QAC9B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QAEvE,MAAM,IAAA,2BAAK,EACT,OAAO,CAAC,QAAQ,EAChB;YACE,cAAc;YACd,WAAW;YACX,qBAAqB;YACrB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChD,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YAC/B,iBAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACtC,qBAAqB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC9C,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE;SACzC,EACD;YACE,GAAG,EAAE,IAAI,CAAC,UAAU;SACrB,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB;;QACtB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QACjD,IAAI,cAAc,EAAE;YAClB,CAAC,CAAC,sCAAsC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YAC3D,IAAI;gBACF,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,GAAG,EAAE;gBACZ,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;gBAEtC,IAAI,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAO,0CAAE,QAAQ,CAAC,gCAAgC,CAAC,EAAE;oBAC5D,MAAM,GAAG,CAAC;iBACX;aACF;SACF;aAAM;YACL,CAAC,CAAC,4CAA4C,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAClE;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjE,IAAI,kBAAkB,EAAE;YACtB,OAAO,EAAE,CAAC;SACX;aAAM;YACL,OAAO;gBACL,oBAAoB;gBACpB,YAAY,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;gBAC5C,cAAc,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;aACzC,CAAC;SACH;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QACrD,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,sEAAsE;QACtE,eAAe;QACf,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YAC9D,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;YAChC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,IAAI,UAAU,CAAC;gBACf,IAAI;oBACF,UAAU,GAAG,MAAM,IAAA,0CAAkB,EAAC,UAAU,CAAC,CAAC;oBAClD,CAAC,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;iBAC/B;gBAAC,OAAO,KAAK,EAAE;oBACd,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;oBAChD,SAAS;iBACV;gBAED,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;oBACpD,YAAY,GAAG,IAAI,CAAC;oBACpB,CAAC,CAAC,8BAA8B,EAAE,GAAG,UAAU,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC9E,MAAM;iBACP;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,CAAC,CAAC,qCAAqC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAExD,8BAA8B;YAC9B,YAAY,GAAG,IAAI,CAAC;SACrB;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,cAAsB;QACjD,MAAM,OAAO,GAAG,MAAM,IAAA,2BAAK,EAAC,OAAO,CAAC,QAAQ,EAAE;YAC5C,cAAc;YACd,QAAQ;YACR,QAAQ;YACR,iBAAiB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YACtC,qBAAqB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;SAC/C,EAAE;YACD,GAAG,EAAE,IAAI,CAAC,UAAU;SACrB,CAAC,CAAC;QAEH,2DAA2D;QAC3D,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;CACF;AAtHD,gCAsHC"}