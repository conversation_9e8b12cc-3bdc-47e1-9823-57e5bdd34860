{"program": {"fileNames": ["../node_modules/typescript/lib/lib.es5.d.ts", "../node_modules/typescript/lib/lib.es2015.d.ts", "../node_modules/typescript/lib/lib.es2016.d.ts", "../node_modules/typescript/lib/lib.es2017.d.ts", "../node_modules/typescript/lib/lib.es2018.d.ts", "../node_modules/typescript/lib/lib.es2015.core.d.ts", "../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../node_modules/typescript/lib/lib.es2017.object.d.ts", "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2017.string.d.ts", "../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../node_modules/chalk/index.d.ts", "../node_modules/@types/minimist/index.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts", "../node_modules/@types/node/ts3.4/base.d.ts", "../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/wasi.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts", "../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/base.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/fs-extra/index.d.ts", "../src/config.ts", "../src/constants.ts", "../src/index.ts", "../src/bin.ts", "../node_modules/@babel/types/lib/index.d.ts", "../node_modules/@types/babel__generator/index.d.ts", "../node_modules/@babel/parser/typings/babel-parser.d.ts", "../node_modules/@types/babel__template/index.d.ts", "../node_modules/@types/babel__traverse/index.d.ts", "../node_modules/@types/babel__core/index.d.ts", "../node_modules/keyv/src/index.d.ts", "../node_modules/@types/http-cache-semantics/index.d.ts", "../node_modules/@types/responselike/index.d.ts", "../node_modules/@types/cacheable-request/index.d.ts", "../node_modules/@types/color-name/index.d.ts", "../node_modules/@types/minimatch/index.d.ts", "../node_modules/@types/glob/index.d.ts", "../node_modules/@types/graceful-fs/index.d.ts", "../node_modules/@types/istanbul-lib-coverage/index.d.ts", "../node_modules/@types/istanbul-lib-report/index.d.ts", "../node_modules/@types/istanbul-reports/index.d.ts", "../node_modules/@jest/expect-utils/build/index.d.ts", "../node_modules/jest-matcher-utils/node_modules/chalk/index.d.ts", "../node_modules/@sinclair/typebox/typebox.d.ts", "../node_modules/@jest/schemas/build/index.d.ts", "../node_modules/pretty-format/build/index.d.ts", "../node_modules/jest-diff/build/index.d.ts", "../node_modules/jest-matcher-utils/build/index.d.ts", "../node_modules/expect/build/index.d.ts", "../node_modules/@types/jest/index.d.ts", "../node_modules/@types/keyv/index.d.ts", "../node_modules/@types/parse-json/index.d.ts", "../node_modules/@types/prettier/index.d.ts", "../node_modules/@types/stack-utils/index.d.ts", "../node_modules/@types/yargs-parser/index.d.ts", "../node_modules/@types/yargs/index.d.ts", "../node_modules/@types/yauzl/index.d.ts"], "fileInfos": [{"version": "f20c05dbfe50a208301d2a1da37b9931bce0466eb5a1f4fe240971b4ecc82b67", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "0d5f52b3174bee6edb81260ebcd792692c32c81fd55499d69531496f3f2b25e7", "affectsGlobalScope": true}, {"version": "55f400eec64d17e888e278f4def2f254b41b89515d3b88ad75d5e05f019daddd", "affectsGlobalScope": true}, {"version": "181f1784c6c10b751631b24ce60c7f78b20665db4550b335be179217bacc0d5f", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "775d9c9fd150d5de79e0450f35bc8b8f94ae64e3eb5da12725ff2a649dccc777", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "e437d83044ba17246a861aa9691aa14223ff4a9d6f338ab1269c41c758586a88", {"version": "68aba9c37b535b42ce96b78a4cfa93813bf4525f86dceb88a6d726c5f7c6c14f", "affectsGlobalScope": true}, "c438b413e94ff76dfa20ae005f33a1c84f2480d1d66e0fd687501020d0de9b50", "bc6a78961535181265845bf9b9e8a147ffd0ca275097ceb670a9b92afa825152", "1fc4b0908c44f39b1f2e5a728d472670a0ea0970d2c6b5691c88167fe541ff82", "123ec69e4b3a686eb49afd94ebe3292a5c84a867ecbcb6bb84bdd720a12af803", {"version": "51851805d06a6878796c3a00ccf0839fe18111a38d1bae84964c269f16bcc2b7", "affectsGlobalScope": true}, "90c85ddbb8de82cd19198bda062065fc51b7407c0f206f2e399e65a52e979720", "dab288a3d92e6a3323566256ba56987b78b100341866fa3cc245c9cd2fd55706", "7ecfe97b43aa6c8b8f90caa599d5648bb559962e74e6f038f73a77320569dd78", "7db7569fbb3e2b01ba8751c761cdd3f0debd104170d5665b7dc20a11630df3a9", {"version": "cde4d7f6274468180fa39847b183aec22626e8212ff885d535c53f4cd7c225fd", "affectsGlobalScope": true}, {"version": "072b0ac82ae8fe05b0d4f2eadb7f6edd0ebd84175ecad2f9e09261290a86bcee", "affectsGlobalScope": true}, "f6eedd1053167b8a651d8d9c70b1772e1b501264a36dfa881d7d4b30d623a9bc", "fb28748ff8d015f52e99daee4f454e57cec1a22141f1257c317f3630a15edeb7", "bbc5e63e6789b5dca3ed78bbeaaca9f6a67bad86e235726f35fdc7831c0fa553", "5d9394b829cfd504b2fe17287aaad8ce1dcfb2a2183c962a90a85b96da2c1c90", "c969bf4c7cdfe4d5dd28aa09432f99d09ad1d8d8b839959646579521d0467d1a", "6c3857edaeeaaf43812f527830ebeece9266b6e8eb5271ab6d2f0008306c9947", "bc6a77e750f4d34584e46b1405b771fb69a224197dd6bafe5b0392a29a70b665", "46cac76114704902baa535b30fb66a26aeaf9430f3b3ab44746e329f12e85498", "ed4ae81196cccc10f297d228bca8d02e31058e6d723a3c5bc4be5fb3c61c6a34", "84044697c8b3e08ef24e4b32cfe6440143d07e469a5e34bda0635276d32d9f35", "6999f789ed86a40f3bc4d7e644e8d42ffda569465969df8077cd6c4e3505dd76", {"version": "0c9f2b308e5696d0802b613aff47c99f092add29408e654f7ab6026134250c18", "affectsGlobalScope": true}, "30ec6f9c683b988c3cfaa0c4690692049c4e7ed7dc6f6e94f56194c06b86f5e1", "884560fda6c3868f925f022adc3a1289fe6507bbb45adb10fa1bbcc73a941bb0", "6b2bb67b0942bcfce93e1d6fad5f70afd54940a2b13df7f311201fba54b2cbe9", "dd3706b25d06fe23c73d16079e8c66ac775831ef419da00716bf2aee530a04a4", "1298327149e93a60c24a3b5db6048f7cc8fd4e3259e91d05fc44306a04b1b873", "d67e08745494b000da9410c1ae2fdc9965fc6d593fe0f381a47491f75417d457", "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "3181290a158e54a78c1a57c41791ec1cbdc860ae565916daa1bf4e425b7edac7", "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "826d48e49c905cedb906cbde6ccaf758827ff5867d4daa006b5a79e0fb489357", "5ef157fbb39494a581bd24f21b60488fe248d452c479738b5e41b48720ea69b8", "289be113bad7ee27ee7fa5b1e373c964c9789a5e9ed7db5ddcb631371120b953", "a1136cf18dbe1b9b600c65538fd48609a1a4772d115a0c1d775839fe6544487c", "e8482f41c6e001657302dcb3a1ba30359a0983574caee9405ef863cb9eac3b95", "f409183966a1dd93d3a9cd1d54fbeb85c73101e87cd5b19467c5e37b252f3fd8", "d44028ae0127eb3e9fcfa5f55a8b81d64775ce15aca1020fe25c511bbb055834", {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "0b3fef11ea6208c4cb3715c9aa108766ce98fc726bfba68cc23b25ce944ce9c0", "0f04bc8950ad634ac8ac70f704f200ef06f8852af9017f97c446de4def5b3546", "08d15b727da0c4f0870d98159311b5d4de1f0293dbb9f1261b6752962d2aa239", "d20072cb51d8baad944bedd935a25c7f10c29744e9a648d2c72c215337356077", "35cbbc58882d2c158032d7f24ba8953d7e1caeb8cb98918d85819496109f55d2", "b4730e5cbeac402e543a0186f3c7521bd7c3c25c8fc7cf048103fc6487566654", {"version": "7452214bbfaf3828400078248e7329ce4ebb62dbd84b8eb82f10dbbe9406c61a", "signature": "9ff11caa5cd56a823ccbc2e246436e65597ffe9b3aeda967109a061b79d7873c"}, {"version": "df70fed175ce319b6e193ee479791f0cbb81c229f4a743e0825372f75a96ab5d", "signature": "50195c7524b59ce7d3c0604315234a7e9e5667f644abdd064a13731636a47b19"}, {"version": "88f228443272c127b0e2b900d4df3c385bfaa8bb15f1cf0f4a0bb92cfc96685a", "signature": "2d332b27462df123c380db9089b54d5c01ccca07bd7ec07e9c1c9df9c3d98fb5"}, {"version": "6bb38ec8413446d4c269ba56c457e7b2a491d92219dee926907ce2e3b261e952", "signature": "43e818adf60173644896298637f47b01d5819b17eda46eaa32d0c7d64724d012"}, "c561efdf5ba0b62619745d4761fe2d9756f23db972e039367d15922fed67fd2f", "cc957354aa3c94c9961ebf46282cfde1e81d107fc5785a61f62c67f1dd3ac2eb", "7ec238b220ea991b6643e24191b1f552a65956d5f6de4c6144e700b9985265d8", "93de1c6dab503f053efe8d304cb522bb3a89feab8c98f307a674a4fae04773e9", "dae3d1adc67ac3dbd1cd471889301339ec439837b5df565982345be20c8fca9a", "5426e62886b7be7806312d31a00e8f7dccd6fe63ba9bbefe99ee2eab29cc48a3", "a7d9d2a35530516e191ade6dc804d7de42d45ff6620c0319cfb4469dbdbd8044", "cab425b5559edac18327eb2c3c0f47e7e9f71b667290b7689faafd28aac69eae", "3cfb0cb51cc2c2e1b313d7c4df04dbf7e5bda0a133c6b309bf6af77cf614b971", "f992cd6cc0bcbaa4e6c810468c90f2d8595f8c6c3cf050c806397d3de8585562", "f0cb4b3ab88193e3e51e9e2622e4c375955003f1f81239d72c5b7a95415dad3e", "963d59066dd6742da1918a6213a209bcc205b8ee53b1876ee2b4e6d80f97c85e", "fd326577c62145816fe1acc306c734c2396487f76719d3785d4e825b34540b33", "3ebae8c00411116a66fca65b08228ea0cf0b72724701f9b854442100aab55aba", "8b06ac3faeacb8484d84ddb44571d8f410697f98d7bfa86c0fda60373a9f5215", "7eb06594824ada538b1d8b48c3925a83e7db792f47a081a62cf3e5c4e23cf0ee", "f5638f7c2f12a9a1a57b5c41b3c1ea7db3876c003bab68e6a57afd6bcc169af0", "763e521cf114b80e0dd0e21ca49b9f8ae62e8999555a5e7bade8ce36b33001c2", "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "3e6bbb0883148627ca0854a9f62d820aaf1a0f1842f5568176721fef156b8f23", "427ce5854885cfc34387e09de05c1d5c1acf94c2143e1693f1d9ff54880573e7", "bed2c4f96fab3348be4a34d88dcb12578c1b2475b07c6acd369e99e227718d81", "e3ba509d3dce019b3190ceb2f3fc88e2610ab717122dabd91a9efaa37804040d", "cda0cb09b995489b7f4c57f168cd31b83dcbaa7aad49612734fb3c9c73f6e4f2", "3ad5991645bbea846d4efe615cd847e785ca30fff0205fdffb0f9a3ade3d13df", {"version": "0dd26bdaa8dfb562d2a600540fa76375f778f34e83fd84fcadc586050e3572ca", "affectsGlobalScope": true}, "fec943fdb3275eb6e006b35e04a8e2e99e9adf3f4b969ddf15315ac7575a93e4", "2b8264b2fefd7367e0f20e2c04eed5d3038831fe00f5efbc110ff0131aab899b", "93c4fc5b5237c09bc9ed65cb8f0dc1d89034406ab40500b89701341994897142", "b0d10e46cfe3f6c476b69af02eaa38e4ccc7430221ce3109ae84bb9fb8282298", "70e9a18da08294f75bf23e46c7d69e67634c0765d355887b9b41f0d959e1426e", "28288f5e5f8b7b895ed2abe6359c1da3e0d14a64b5aef985071285671f347c01", "65dfa4bc49ccd1355789abb6ae215b302a5b050fdee9651124fe7e826f33113c"], "options": {"allowSyntheticDefaultImports": true, "declaration": true, "module": 1, "noImplicitAny": true, "outDir": "./", "sourceMap": true, "strict": true, "target": 4}, "fileIdsList": [[83], [102], [83, 84, 85, 86, 87], [83, 85], [42, 45, 65, 77, 89, 90, 91], [43, 77], [42, 43, 77, 94], [97], [98], [104, 107], [42, 77], [74, 75], [42, 43, 50, 59], [34, 42, 50], [66], [38, 43, 51], [59], [40, 42, 50], [42], [42, 44, 59, 65], [43], [50, 59, 65], [42, 43, 45, 50, 59, 62, 65], [42, 45, 62, 65], [76], [65], [40, 42, 59], [32], [64], [42, 59], [57, 66, 68], [38, 40, 50, 59], [31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70], [71, 72, 73], [50], [56], [42, 59, 65, 68], [45, 59, 77], [113], [42, 59, 77], [100, 106], [104], [101, 105], [103], [29, 30, 52, 79, 80, 81], [34, 52, 78, 79, 80], [79, 80]], "referencedMap": [[85, 1], [103, 2], [88, 3], [84, 1], [86, 4], [87, 1], [92, 5], [78, 6], [95, 7], [96, 6], [98, 8], [99, 9], [108, 10], [109, 11], [76, 12], [34, 13], [35, 14], [36, 15], [37, 16], [38, 17], [39, 18], [41, 19], [43, 20], [44, 21], [45, 22], [46, 23], [47, 24], [77, 25], [48, 19], [49, 26], [50, 27], [53, 28], [54, 29], [57, 30], [58, 31], [59, 19], [62, 32], [71, 33], [74, 34], [64, 35], [65, 36], [67, 17], [69, 37], [70, 17], [91, 38], [114, 39], [115, 40], [107, 41], [105, 42], [106, 43], [89, 19], [104, 44], [82, 45], [81, 46]], "exportedModulesMap": [[85, 1], [103, 2], [88, 3], [84, 1], [86, 4], [87, 1], [92, 5], [78, 6], [95, 7], [96, 6], [98, 8], [99, 9], [108, 10], [109, 11], [76, 12], [34, 13], [35, 14], [36, 15], [37, 16], [38, 17], [39, 18], [41, 19], [43, 20], [44, 21], [45, 22], [46, 23], [47, 24], [77, 25], [48, 19], [49, 26], [50, 27], [53, 28], [54, 29], [57, 30], [58, 31], [59, 19], [62, 32], [71, 33], [74, 34], [64, 35], [65, 36], [67, 17], [69, 37], [70, 17], [91, 38], [114, 39], [115, 40], [107, 41], [105, 42], [106, 43], [89, 19], [104, 44], [81, 47]], "semanticDiagnosticsPerFile": [85, 83, 100, 103, 102, 88, 84, 86, 87, 92, 93, 78, 95, 96, 90, 97, 98, 99, 108, 109, 94, 30, 75, 32, 76, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 31, 72, 45, 46, 47, 77, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 71, 74, 64, 65, 66, 67, 68, 73, 69, 70, 110, 111, 91, 112, 113, 114, 115, 29, 107, 105, 106, 101, 89, 104, 7, 6, 2, 8, 9, 10, 11, 12, 13, 14, 15, 3, 4, 19, 16, 17, 18, 20, 21, 22, 5, 23, 24, 25, 26, 27, 1, 28, 82, 79, 80, 81]}, "version": "4.8.3"}