/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11.25 17.25h1.5L12 18z", key: "1wmwwj" }],
  ["path", { d: "m15 12 2 2", key: "k60wz4" }],
  ["path", { d: "M18 6.5a.5.5 0 0 0-.5-.5", key: "1ch4h4" }],
  [
    "path",
    {
      d: "M20.69 9.67a4.5 4.5 0 1 0-7.04-5.5 8.35 8.35 0 0 0-3.3 0 4.5 4.5 0 1 0-7.04 5.5C2.49 11.2 2 12.88 2 14.5 2 19.47 6.48 22 12 22s10-2.53 10-7.5c0-1.62-.48-3.3-1.3-4.83",
      key: "1c660l"
    }
  ],
  ["path", { d: "M6 6.5a.495.495 0 0 1 .5-.5", key: "eviuep" }],
  ["path", { d: "m9 12-2 2", key: "326nkw" }]
];
const Panda = createLucideIcon("panda", __iconNode);

export { __iconNode, Panda as default };
//# sourceMappingURL=panda.js.map
