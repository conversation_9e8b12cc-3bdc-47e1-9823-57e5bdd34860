#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/electron-builder@26.0.12_el_8899b04457df31c5ca5224f018552f9e/node_modules/electron-builder/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/electron-builder@26.0.12_el_8899b04457df31c5ca5224f018552f9e/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/electron-builder@26.0.12_el_8899b04457df31c5ca5224f018552f9e/node_modules/electron-builder/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/electron-builder@26.0.12_el_8899b04457df31c5ca5224f018552f9e/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../electron-builder/cli.js" "$@"
else
  exec node  "$basedir/../electron-builder/cli.js" "$@"
fi
