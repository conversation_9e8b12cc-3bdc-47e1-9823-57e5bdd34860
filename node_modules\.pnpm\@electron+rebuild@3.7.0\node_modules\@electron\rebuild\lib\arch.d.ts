/**
 * Runs the `uname` command and returns the trimmed output.
 *
 * Copied from `@electron/get`.
 */
export declare function uname(): string;
export declare type ConfigVariables = {
    arm_version?: string;
};
/**
 * Generates an architecture name that would be used in an Electron or Node.js
 * download file name.
 *
 * Copied from `@electron/get`.
 */
export declare function getNodeArch(arch: string, configVariables: ConfigVariables): string;
