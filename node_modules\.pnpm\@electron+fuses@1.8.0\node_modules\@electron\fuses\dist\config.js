"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuseV1Options = exports.FuseVersion = void 0;
var FuseVersion;
(function (FuseVersion) {
    FuseVersion["V1"] = "1";
})(FuseVersion = exports.FuseVersion || (exports.FuseVersion = {}));
/**
 * Maps config keys to their index in the fuse wire
 */
var FuseV1Options;
(function (FuseV1Options) {
    FuseV1Options[FuseV1Options["RunAsNode"] = 0] = "RunAsNode";
    FuseV1Options[FuseV1Options["EnableCookieEncryption"] = 1] = "EnableCookieEncryption";
    FuseV1Options[FuseV1Options["EnableNodeOptionsEnvironmentVariable"] = 2] = "EnableNodeOptionsEnvironmentVariable";
    FuseV1Options[FuseV1Options["EnableNodeCliInspectArguments"] = 3] = "EnableNodeCliInspectArguments";
    FuseV1Options[FuseV1Options["EnableEmbeddedAsarIntegrityValidation"] = 4] = "EnableEmbeddedAsarIntegrityValidation";
    FuseV1Options[FuseV1Options["OnlyLoadAppFromAsar"] = 5] = "OnlyLoadAppFromAsar";
    FuseV1Options[FuseV1Options["LoadBrowserProcessSpecificV8Snapshot"] = 6] = "LoadBrowserProcessSpecificV8Snapshot";
    FuseV1Options[FuseV1Options["GrantFileProtocolExtraPrivileges"] = 7] = "GrantFileProtocolExtraPrivileges";
})(FuseV1Options = exports.FuseV1Options || (exports.FuseV1Options = {}));
//# sourceMappingURL=config.js.map