<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل البرنامج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: white;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
        .loading { background: rgba(255, 152, 0, 0.3); }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار تحميل ملفات البرنامج</h1>
        <p>👨‍💻 المبرمج: علي عاجل خشان المحنّة</p>
        
        <div id="test-results">
            <div class="test-item loading">
                <h3>🔄 جاري فحص الملفات...</h3>
            </div>
        </div>
    </div>

    <script>
        const testResults = document.getElementById('test-results');
        
        function addTestResult(title, status, message) {
            const div = document.createElement('div');
            div.className = `test-item ${status}`;
            div.innerHTML = `
                <h3>${status === 'success' ? '✅' : status === 'error' ? '❌' : '🔄'} ${title}</h3>
                <p>${message}</p>
            `;
            testResults.appendChild(div);
        }

        function runTests() {
            testResults.innerHTML = '';
            
            // اختبار 1: فحص وجود مجلد dist
            addTestResult('فحص مجلد dist', 'loading', 'جاري التحقق من وجود مجلد dist...');
            
            // اختبار 2: فحص ملف CSS
            const cssLink = document.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = './dist/assets/index-QQmYXolK.css';
            cssLink.onload = function() {
                addTestResult('ملف CSS', 'success', 'تم تحميل ملف CSS بنجاح');
            };
            cssLink.onerror = function() {
                addTestResult('ملف CSS', 'error', 'فشل في تحميل ملف CSS: ./dist/assets/index-QQmYXolK.css');
            };
            document.head.appendChild(cssLink);
            
            // اختبار 3: فحص ملف JavaScript
            const script = document.createElement('script');
            script.type = 'module';
            script.src = './dist/assets/index-BWWQOl9G.js';
            script.onload = function() {
                addTestResult('ملف JavaScript', 'success', 'تم تحميل ملف JavaScript بنجاح');
                
                // اختبار 4: فحص React
                setTimeout(() => {
                    if (window.React) {
                        addTestResult('مكتبة React', 'success', 'تم تحميل React بنجاح');
                    } else {
                        addTestResult('مكتبة React', 'error', 'لم يتم تحميل React');
                    }
                    
                    // اختبار 5: فحص ReactDOM
                    if (window.ReactDOM) {
                        addTestResult('مكتبة ReactDOM', 'success', 'تم تحميل ReactDOM بنجاح');
                    } else {
                        addTestResult('مكتبة ReactDOM', 'error', 'لم يتم تحميل ReactDOM');
                    }
                    
                    // اختبار 6: فحص عنصر root
                    const rootElement = document.getElementById('root');
                    if (rootElement) {
                        addTestResult('عنصر Root', 'success', 'تم العثور على عنصر root');
                        
                        // فحص محتوى root
                        setTimeout(() => {
                            if (rootElement.children.length > 0) {
                                addTestResult('محتوى التطبيق', 'success', 'تم تحميل محتوى التطبيق بنجاح');
                            } else {
                                addTestResult('محتوى التطبيق', 'error', 'عنصر root فارغ - لم يتم تحميل التطبيق');
                            }
                        }, 3000);
                    } else {
                        addTestResult('عنصر Root', 'error', 'لم يتم العثور على عنصر root');
                    }
                }, 1000);
            };
            script.onerror = function() {
                addTestResult('ملف JavaScript', 'error', 'فشل في تحميل ملف JavaScript: ./dist/assets/index-BWWQOl9G.js');
            };
            document.head.appendChild(script);
            
            // اختبار 7: فحص favicon
            const favicon = document.createElement('link');
            favicon.rel = 'icon';
            favicon.href = './dist/favicon.ico';
            favicon.onload = function() {
                addTestResult('أيقونة الموقع', 'success', 'تم تحميل favicon بنجاح');
            };
            favicon.onerror = function() {
                addTestResult('أيقونة الموقع', 'error', 'فشل في تحميل favicon');
            };
            document.head.appendChild(favicon);
        }

        // إضافة عنصر root للاختبار
        const rootDiv = document.createElement('div');
        rootDiv.id = 'root';
        rootDiv.style.display = 'none';
        document.body.appendChild(rootDiv);

        // بدء الاختبارات
        runTests();
        
        // إضافة معلومات إضافية
        setTimeout(() => {
            addTestResult('معلومات المتصفح', 'success', 
                `المتصفح: ${navigator.userAgent.split(' ')[0]}<br>
                 الموقع: ${window.location.href}<br>
                 البروتوكول: ${window.location.protocol}`
            );
        }, 2000);
    </script>
</body>
</html>
