#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/asar/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/asar/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/asar/bin/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/asar/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules/@electron/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/@electron+asar@3.2.18/node_modules:/mnt/c/Users/<USER>/work-attendance-system/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@electron+asar@3.2.18/node_modules/@electron/asar/bin/asar.js" "$@"
else
  exec node  "$basedir/../../../../../../@electron+asar@3.2.18/node_modules/@electron/asar/bin/asar.js" "$@"
fi
